import pandas as pd
import logging
from typing import Dict, Tuple, Optional, List

logger = logging.getLogger(__name__)

class BirdTaxonomy:
    """处理鸟类分类层级关系，提供种名到目科属的映射"""
    
    def __init__(self, excel_path: str):
        """初始化鸟类分类层级关系
        
        Args:
            excel_path: Excel文件路径，包含鸟类分类信息
        """
        self.taxonomy_map = {}
        self.load_taxonomy(excel_path)
    
    def load_taxonomy(self, excel_path: str) -> None:
        """从Excel文件加载鸟类分类层级关系
        
        Args:
            excel_path: Excel文件路径
        """
        try:
            df = pd.read_excel(excel_path)
            # 确保必要的列存在
            required_columns = ['中文名', '目', '科', '属', '种']
            for col in required_columns:
                if col not in df.columns:
                    logger.error(f"Excel文件缺少必要的列: {col}")
                    raise ValueError(f"Excel文件缺少必要的列: {col}")
            
            # 构建种名到目科属的映射
            for _, row in df.iterrows():
                species = row['种']
                chinese_name = row['中文名']
                order = row['目']
                family = row['科']
                genus = row['属']
                
                # 使用种名和中文名作为键
                self.taxonomy_map[species] = {
                    'order': order,
                    'family': family,
                    'genus': genus,
                    'species': species,
                    'chinese_name': chinese_name
                }
                
                # 也添加中文名作为键
                if chinese_name != species:
                    self.taxonomy_map[chinese_name] = {
                        'order': order,
                        'family': family,
                        'genus': genus,
                        'species': species,
                        'chinese_name': chinese_name
                    }
            
            logger.info(f"成功加载鸟类分类信息，共 {len(self.taxonomy_map)} 条记录")
        
        except Exception as e:
            logger.error(f"加载鸟类分类信息失败: {str(e)}")
            raise
    
    def get_taxonomy(self, bird_name: str) -> Optional[Dict]:
        """获取鸟类的分类信息
        
        Args:
            bird_name: 鸟类名称（种名或中文名）
            
        Returns:
            包含目、科、属、种信息的字典，如果未找到则返回None
        """
        return self.taxonomy_map.get(bird_name)
    
    def get_hierarchy_names(self, bird_name: str) -> Tuple[Optional[str], Optional[str], Optional[str], Optional[str]]:
        """获取鸟类的层级名称（目、科、属、种）
        
        Args:
            bird_name: 鸟类名称（种名或中文名）
            
        Returns:
            (目, 科, 属, 种)的元组，如果未找到则相应位置为None
        """
        taxonomy = self.get_taxonomy(bird_name)
        if taxonomy:
            return (
                taxonomy.get('order'),
                taxonomy.get('family'),
                taxonomy.get('genus'),
                taxonomy.get('species')
            )
        return None, None, None, None
    
    def is_same_hierarchy(self, name1: str, name2: str, level: str) -> bool:
        """判断两个鸟类名称在指定层级是否相同
        
        Args:
            name1: 第一个鸟类名称
            name2: 第二个鸟类名称
            level: 层级，可以是'order'（目）, 'family'（科）, 'genus'（属）或'species'（种）
            
        Returns:
            如果在指定层级相同则返回True，否则返回False
        """
        taxonomy1 = self.get_taxonomy(name1)
        taxonomy2 = self.get_taxonomy(name2)
        
        if not taxonomy1 or not taxonomy2:
            return False
        
        return taxonomy1.get(level) == taxonomy2.get(level)