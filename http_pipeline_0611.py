import json
import time
from collections import defaultdict, Counter

import torch
import cv2
import pandas as pd
import numpy as np
import torch.nn as nn
from timm import create_model
from torchvision import transforms
from ultralytics import YOLO
from flask import Flask, request, jsonify
import logging
from typing import Dict, Any, List, Tuple, Optional
from scipy.optimize import linear_sum_assignment
from filterpy.kalman import KalmanFilter
from bird_taxonomy import BirdTaxonomy

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ObjectTracker:
    """Tracks objects across frames and maintains classification history."""
    def __init__(self, max_disappeared=30, max_distance=100.0):
        # Store the next unique object ID along with active and disappeared objects
        self.next_object_id = 0
        self.objects = {}  # {object_id: centroid}
        self.disappeared = {}  # {object_id: count}
        self.classification_history = {}  # {object_id: [(label, confidence, timestamp, hierarchy_info), ...]}
        
        # 存储每个对象的最高置信度分类信息，改为嵌套字典结构，独立存储每个分类层级
        self.best_classifications = {}  # {object_id: {'species': {'name': name, 'score': score}, 'genus': {...}, ...}}
        
        # Maximum number of frames an object can be marked as disappeared
        self.max_disappeared = max_disappeared
        
        # Maximum distance between centroids to consider as the same object
        self.max_distance = max_distance
        
        # 卡尔曼滤波器字典，为每个跟踪对象创建一个滤波器
        self.kalman_filters = {}
        
        # 移动平均滤波窗口大小
        self.ma_window_size = 2
        self.position_history = {}  # {object_id: [(cx, cy), ...]}
        
        # 置信度衰减系数 - 每帧衰减因子，24帧/秒的视频中约5秒内从1降低到0.95
        self.confidence_decay_factor = 0.999
        
        # 存储最近被注销的对象信息，用于ID恢复
        self.recently_deregistered = {}  # {object_id: {'centroid': (x, y), 'timestamp': time, 'classification': {...}}}
        # 最近注销的对象保留时间（秒）
        self.deregistered_retention_time = 5.0
        # 恢复ID时使用的距离阈值，调整为更大的值以提高ID恢复成功率
        self.recovery_distance_threshold = 100.0
        
        # Load bird taxonomy information
        self.taxonomy = BirdTaxonomy('鸟类目科属种.xlsx')
    
    def register(self, centroid):
        """Register a new object with a new ID."""
        object_id = self.next_object_id
        self.objects[object_id] = centroid
        self.disappeared[object_id] = 0
        self.classification_history[object_id] = []
        self.position_history[object_id] = [centroid]
        
        # 初始化卡尔曼滤波器
        kf = KalmanFilter(dim_x=4, dim_z=2)  # 状态: [x, y, vx, vy], 观测: [x, y]
        dt = 1.0  # 时间步长
        
        # 状态转移矩阵 (F)
        kf.F = np.array([
            [1, 0, dt, 0],
            [0, 1, 0, dt],
            [0, 0, 1, 0],
            [0, 0, 0, 1]
        ])
        
        # 测量函数 (H)
        kf.H = np.array([
            [1, 0, 0, 0],
            [0, 1, 0, 0]
        ])
        
        # 测量噪声协方差 (R)
        kf.R = np.array([
            [5, 0],
            [0, 5]
        ])
        
        # 过程噪声协方差 (Q)
        q = 0.1  # 过程噪声参数
        kf.Q = np.array([
            [q, 0, 0, 0],
            [0, q, 0, 0],
            [0, 0, q, 0],
            [0, 0, 0, q]
        ])
        
        # 后验误差协方差 (P)
        kf.P = np.array([
            [10, 0, 0, 0],
            [0, 10, 0, 0],
            [0, 0, 10, 0],
            [0, 0, 0, 10]
        ])
        
        # 初始状态 (x)
        kf.x = np.array([centroid[0], centroid[1], 0, 0]).reshape(4, 1)
        
        self.kalman_filters[object_id] = kf
        self.next_object_id += 1
        return object_id
    
    def deregister(self, object_id):
        """Deregister an object that has disappeared for too long.
        
        保存被注销对象的信息到recently_deregistered字典中，以便后续可能的ID恢复
        """
        # 保存对象信息到recently_deregistered字典中
        current_time = time.time()
        self.recently_deregistered[object_id] = {
            'centroid': self.objects[object_id],
            'timestamp': current_time,
            'classification': self.best_classifications.get(object_id, {}),
            'kalman_filter': self.kalman_filters.get(object_id, None),
            'position_history': self.position_history.get(object_id, [])
        }
        
        # 清理过期的recently_deregistered条目
        expired_ids = []
        for old_id, info in self.recently_deregistered.items():
            if current_time - info['timestamp'] > self.deregistered_retention_time:
                expired_ids.append(old_id)
        
        for old_id in expired_ids:
            del self.recently_deregistered[old_id]
        
        # 正常的注销流程
        del self.objects[object_id]
        del self.disappeared[object_id]
        del self.classification_history[object_id]
        if object_id in self.best_classifications:
            del self.best_classifications[object_id]
        if object_id in self.kalman_filters:
            del self.kalman_filters[object_id]
        if object_id in self.position_history:
            del self.position_history[object_id]
    
    def update(self, boxes):
        """Update tracked objects with new detections using Hungarian algorithm.
        
        Args:
            boxes: List of bounding boxes in format [x1, y1, x2, y2]
            
        Returns:
            Dictionary mapping box indices to object IDs
        """
        # 对所有对象的最高置信度进行衰减
        for object_id in self.best_classifications:
            for level in ['species', 'genus', 'family', 'order']:
                if self.best_classifications[object_id][level]['score'] > 0:
                    self.best_classifications[object_id][level]['score'] *= self.confidence_decay_factor
        
        # If no boxes, mark all existing objects as disappeared
        if len(boxes) == 0:
            for object_id in list(self.disappeared.keys()):
                self.disappeared[object_id] += 1
                if self.disappeared[object_id] > self.max_disappeared:
                    self.deregister(object_id)
            return {}
        
        # Calculate centroids of current boxes
        centroids = []
        for box in boxes:
            x1, y1, x2, y2 = box
            cx = (x1 + x2) / 2.0
            cy = (y1 + y2) / 2.0
            centroids.append((cx, cy))
        
        # If we have no objects yet, register all
        if len(self.objects) == 0:
            box_to_id = {}
            for i, centroid in enumerate(centroids):
                object_id = self.register(centroid)
                box_to_id[i] = object_id
            return box_to_id
        
        # 预测所有现有对象的新位置（使用卡尔曼滤波器）
        object_ids = list(self.objects.keys())
        predicted_centroids = []
        
        for object_id in object_ids:
            if object_id in self.kalman_filters:
                kf = self.kalman_filters[object_id]
                # 预测步骤
                kf.predict()
                predicted_centroid = (kf.x[0, 0], kf.x[1, 0])  # 提取预测的位置
                predicted_centroids.append(predicted_centroid)
            else:
                # 如果没有卡尔曼滤波器，使用最后已知位置
                predicted_centroids.append(self.objects[object_id])
        
        # 计算预测位置与新检测到的中心点之间的距离矩阵
        distances = np.zeros((len(predicted_centroids), len(centroids)))
        for i, predicted_centroid in enumerate(predicted_centroids):
            for j, centroid in enumerate(centroids):
                distances[i, j] = np.sqrt(
                    (predicted_centroid[0] - centroid[0]) ** 2 + 
                    (predicted_centroid[1] - centroid[1]) ** 2
                )
        
        # 应用匈牙利算法找到全局最优匹配
        # 如果距离大于max_distance，则设置为一个很大的值，避免匹配
        cost_matrix = distances.copy()
        cost_matrix[cost_matrix > self.max_distance] = 1000000  # 设置一个很大的值
        
        # 使用匈牙利算法找到最优匹配
        row_indices, col_indices = linear_sum_assignment(cost_matrix)
        
        # 创建匹配结果字典
        box_to_id = {}
        
        # 处理匹配结果
        for row, col in zip(row_indices, col_indices):
            # 如果距离太大，跳过此匹配
            if distances[row, col] > self.max_distance:
                continue
                
            # 获取对象ID
            object_id = object_ids[row]
            
            # 更新对象位置
            self.objects[object_id] = centroids[col]
            
            # 更新位置历史记录（用于移动平均滤波）
            if object_id in self.position_history:
                self.position_history[object_id].append(centroids[col])
                # 保持历史记录在窗口大小范围内
                if len(self.position_history[object_id]) > self.ma_window_size:
                    self.position_history[object_id] = self.position_history[object_id][-self.ma_window_size:]
                
                # 应用移动平均滤波
                if len(self.position_history[object_id]) >= 2:
                    # 计算平均位置
                    avg_x = sum(p[0] for p in self.position_history[object_id]) / len(self.position_history[object_id])
                    avg_y = sum(p[1] for p in self.position_history[object_id]) / len(self.position_history[object_id])
                    
                    # 使用平滑后的位置
                    smoothed_centroid = (avg_x, avg_y)
                    self.objects[object_id] = smoothed_centroid
            
            # 更新卡尔曼滤波器
            if object_id in self.kalman_filters:
                kf = self.kalman_filters[object_id]
                # 更新步骤
                measurement = np.array([centroids[col][0], centroids[col][1]]).reshape(2, 1)
                kf.update(measurement)
            
            # 重置消失计数
            self.disappeared[object_id] = 0
            
            # 记录匹配结果
            box_to_id[col] = object_id
        
        # 处理未匹配的现有对象（可能消失的对象）
        matched_rows = set(row_indices)
        unmatched_rows = set(range(len(object_ids))) - matched_rows
        
        for row in unmatched_rows:
            object_id = object_ids[row]
            self.disappeared[object_id] += 1
            if self.disappeared[object_id] > self.max_disappeared:
                self.deregister(object_id)
        
        # 处理未匹配的新检测（新出现的对象）
        matched_cols = set(col_indices)
        unmatched_cols = set(range(len(centroids))) - matched_cols
        
        for col in unmatched_cols:
            # 尝试恢复最近消失的ID
            recovered_id = None
            current_centroid = centroids[col]
            
            # 检查是否有最近消失的对象在相似位置
            for old_id, info in self.recently_deregistered.items():
                old_centroid = info['centroid']
                distance = np.sqrt(
                    (old_centroid[0] - current_centroid[0]) ** 2 + 
                    (old_centroid[1] - current_centroid[1]) ** 2
                )
                
                # 如果距离小于恢复阈值，恢复此ID
                if distance < self.recovery_distance_threshold:
                    recovered_id = old_id
                    
                    # 恢复对象信息
                    self.objects[old_id] = current_centroid
                    self.disappeared[old_id] = 0
                    
                    # 恢复分类历史
                    if old_id not in self.classification_history:
                        self.classification_history[old_id] = []
                    
                    # 恢复最佳分类信息
                    if 'classification' in info and info['classification']:
                        self.best_classifications[old_id] = info['classification']
                    
                    # 恢复卡尔曼滤波器
                    if 'kalman_filter' in info and info['kalman_filter'] is not None:
                        self.kalman_filters[old_id] = info['kalman_filter']
                        # 更新卡尔曼滤波器的状态
                        kf = self.kalman_filters[old_id]
                        measurement = np.array([current_centroid[0], current_centroid[1]]).reshape(2, 1)
                        kf.update(measurement)
                    else:
                        # 如果没有保存卡尔曼滤波器，创建一个新的
                        kf = KalmanFilter(dim_x=4, dim_z=2)
                        dt = 1.0
                        kf.F = np.array([
                            [1, 0, dt, 0],
                            [0, 1, 0, dt],
                            [0, 0, 1, 0],
                            [0, 0, 0, 1]
                        ])
                        kf.H = np.array([
                            [1, 0, 0, 0],
                            [0, 1, 0, 0]
                        ])
                        kf.R = np.array([
                            [5, 0],
                            [0, 5]
                        ])
                        q = 0.1
                        kf.Q = np.array([
                            [q, 0, 0, 0],
                            [0, q, 0, 0],
                            [0, 0, q, 0],
                            [0, 0, 0, q]
                        ])
                        kf.P = np.array([
                            [10, 0, 0, 0],
                            [0, 10, 0, 0],
                            [0, 0, 10, 0],
                            [0, 0, 0, 10]
                        ])
                        kf.x = np.array([current_centroid[0], current_centroid[1], 0, 0]).reshape(4, 1)
                        self.kalman_filters[old_id] = kf
                    
                    # 恢复位置历史
                    if 'position_history' in info and info['position_history']:
                        self.position_history[old_id] = info['position_history']
                    else:
                        self.position_history[old_id] = [current_centroid]
                    
                    # 从recently_deregistered中移除此ID
                    del self.recently_deregistered[old_id]
                    break
            
            # 如果没有恢复ID，则注册新ID
            if recovered_id is None:
                object_id = self.register(current_centroid)
                box_to_id[col] = object_id
            else:
                box_to_id[col] = recovered_id
                
            # 确保所有未匹配的新检测都被添加到box_to_id中
            # 这是修复从边缘移入的新物体无法获得ID的关键
        
        return box_to_id
    
    def add_classification(self, object_id, label, confidence):
        """Add a classification result to an object's history.
        
        分别维护每个层级（种、属、科、目）的最高置信度，只在当前分类结果的某层级置信度超过历史最高分时更新该层级
        增加了置信度阈值，防止低置信度的分类替换高置信度的分类
        """
        if object_id in self.classification_history:
            # Get hierarchy information for the label
            hierarchy_info = self.taxonomy.get_taxonomy(label)
            if hierarchy_info:
                current_time = time.time()
                
                # 添加到历史记录
                self.classification_history[object_id].append({
                    'label': label,
                    'confidence': confidence,
                    'timestamp': current_time,
                    'order': hierarchy_info['order'],
                    'family': hierarchy_info['family'],
                    'genus': hierarchy_info['genus'],
                    'species': hierarchy_info['species']
                })
                
                # 初始化最高置信度分类信息（如果不存在）
                if object_id not in self.best_classifications:
                    self.best_classifications[object_id] = {
                        'species': {'name': hierarchy_info['species'], 'score': confidence},
                        'genus': {'name': hierarchy_info['genus'], 'score': confidence},
                        'family': {'name': hierarchy_info['family'], 'score': confidence},
                        'order': {'name': hierarchy_info['order'], 'score': confidence}
                    }
                    return
                
                # 分别检查并更新每个层级的最高置信度
                best_info = self.best_classifications[object_id]
                
                # 更新种级别
                species_name = hierarchy_info['species']
                if species_name:
                    current_species_score = best_info['species']['score']
                    if (confidence > current_species_score + 0.1 or 
                        (confidence > current_species_score and current_species_score < 0.6)):
                        best_info['species']['name'] = species_name
                        best_info['species']['score'] = confidence
                
                # 更新属级别
                genus_name = hierarchy_info['genus']
                if genus_name:
                    current_genus_score = best_info['genus']['score']
                    if (confidence > current_genus_score + 0.1 or 
                        (confidence > current_genus_score and current_genus_score < 0.6)):
                        best_info['genus']['name'] = genus_name
                        best_info['genus']['score'] = confidence
                
                # 更新科级别
                family_name = hierarchy_info['family']
                if family_name:
                    current_family_score = best_info['family']['score']
                    if (confidence > current_family_score + 0.1 or 
                        (confidence > current_family_score and current_family_score < 0.6)):
                        best_info['family']['name'] = family_name
                        best_info['family']['score'] = confidence
                
                # 更新目级别
                order_name = hierarchy_info['order']
                if order_name:
                    current_order_score = best_info['order']['score']
                    if (confidence > current_order_score + 0.1 or 
                        (confidence > current_order_score and current_order_score < 0.6)):
                        best_info['order']['name'] = order_name
                        best_info['order']['score'] = confidence

    def get_best_label(self, object_id, confidence_threshold=0.7):
        """Get the best label for an object based on classification history.
        
        根据客户端输入的置信度阈值，取出其对应符合要求的层级和置信度
        保持标签的稳定性，避免标签跳变
        
        Args:
            object_id: The ID of the object
            confidence_threshold: Threshold for high-confidence classifications
            
        Returns:
            The best label for the object, or None if all confidence scores are below threshold
        """
        if object_id not in self.best_classifications:
            return None
        
        best_info = self.best_classifications[object_id]
        
        # 按照种、属、科、目的顺序检查置信度是否超过阈值
        if best_info['species']['score'] >= confidence_threshold and best_info['species']['name']:
            return best_info['species']['name']
        
        if best_info['genus']['score'] >= confidence_threshold and best_info['genus']['name']:
            return best_info['genus']['name']
            
        if best_info['family']['score'] >= confidence_threshold and best_info['family']['name']:
            return best_info['family']['name']
            
        if best_info['order']['score'] >= confidence_threshold and best_info['order']['name']:
            return best_info['order']['name']
            
        # 如果所有层级都不满足条件，返回None表示没有高置信度的分类
        # 这样可以避免低置信度的种名被错误地与其他层级置信度绑定
        return None


class BirdDetector:
    def __init__(self, yolo_path, classifier_path, class_dict_path, device=None):
        if device is None:
            self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        else:
            self.device = device

        logger.info(f"Initializing BirdDetector on device: {self.device}")

        self.yolo_model = YOLO(yolo_path).to(self.device)
        self.class_dict = self._load_class_dict(class_dict_path)
        num_classes = len(self.class_dict)
        self.classifier = self._build_classifier(classifier_path, num_classes)

        self.transform = transforms.Compose([
            transforms.ToPILImage(),
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(
                mean=[0.485, 0.456, 0.406],
                std=[0.229, 0.224, 0.225]
            )
        ])

        self.sr = cv2.dnn_superres.DnnSuperResImpl_create()
        self.sr.readModel('FSRCNN_x4.pb')
        self.sr.setModel('fsrcnn', 4)
        
        # Initialize object tracker
        self.tracker = ObjectTracker(max_disappeared=30, max_distance=75.0)
        
        # Store the last processed frame and results for temporal consistency
        self.last_frame = None
        self.last_results = None

        logger.info("BirdDetector initialization completed")

    def _build_classifier(self, classifier_path, num_classes):
        """Build and load the Swin Transformer classifier model."""
        try:
            model = create_model(
                'swin_small_patch4_window7_224',
                pretrained=False,
                num_classes=num_classes,
                drop_rate=0.1,
                drop_path_rate=0.1
            )
            model.to(self.device)
            ckpt = torch.load(classifier_path, map_location=self.device)
            model.load_state_dict(ckpt['model_state_dict'])
            model.eval()
            return model
        except Exception as e:
            logging.error(f"Error in _build_classifier: {str(e)}")
            raise

    def _load_class_dict(self, class_dict_path):
        """Load the class dictionary from JSON file."""
        try:
            with open(class_dict_path, 'r', encoding='utf-8') as file:
                class_dict = json.load(file)
                # Convert string keys to integers for consistent lookup
                return {int(k): v for k, v in class_dict.items()}
        except Exception as e:
            logger.error(f"Error in _load_class_dict: {str(e)}")
            raise

    def detect_and_classify(self, image_array, threshold=0.3, use_tracking=True, confidence_threshold=0.7):
        # threshold参数用于YOLO检测的置信度阈值，设置为较低的值以确保不会漏检
        # confidence_threshold参数用于分类模型的置信度阈值，由客户端传入
        results = self.yolo_model(image_array)
        result = results[0]

        if len(result) == 0:
            self.last_frame = image_array.copy()
            self.last_results = {'boxes': [], 'labels': [], 'confidences': [], 'object_ids': [], 'detections_list': []}
            return self.last_results

        # 使用固定的较低阈值进行YOLO检测，确保不会漏检目标
        yolo_threshold = 0.3  # 固定YOLO检测阈值为0.3
        mask = result.boxes.conf.cpu().numpy() >= yolo_threshold

        if not any(mask):
            self.last_frame = image_array.copy()
            self.last_results = {'boxes': [], 'labels': [], 'confidences': [], 'object_ids': [], 'detections_list': []}
            return self.last_results

        boxes = result.boxes.xyxy[mask].cpu().numpy()
        confidences = result.boxes.conf[mask].cpu().numpy()
        labels = []
        object_ids = []
        detections_list = []  # 存储每个框的前5个预测结果

        # Update object tracking
        if use_tracking:
            box_to_id = self.tracker.update(boxes)
        
        # Process each detected box
        for i, box in enumerate(result.boxes.xyxy[mask]):
            x1, y1, x2, y2 = map(int, box.cpu().numpy())
            crop = image_array[y1:y2, x1:x2]
            #crop = self.sr.upsample(crop)
            crop = cv2.cvtColor(crop, cv2.COLOR_BGR2RGB)
            crop = self.transform(crop)
            crop = torch.unsqueeze(crop, 0).to(self.device)

            with torch.no_grad():
                outputs = self.classifier(crop)
                prob = nn.LogSoftmax(dim=1)(outputs)
                # 获取前5个预测结果
                top5_probs, top5_indices = torch.topk(prob, 5, dim=1)
                top5_probs = torch.exp(top5_probs).cpu().numpy()[0]  # 转换回概率
                top5_indices = top5_indices.cpu().numpy()[0]
                
                # 获取前5个类别名称和概率
                top5_detections = []
                for j, (idx, prob_val) in enumerate(zip(top5_indices, top5_probs)):
                    top5_detections.append({
                        "name": self.class_dict[idx],
                        "score": str(round(float(prob_val), 6))
                    })
                
                # 保存前5个预测结果
                detections_list.append(top5_detections)
                
                # 仍然保留最高置信度的预测作为主标签
                pred = top5_indices[0]
                label = self.class_dict[pred]
            # 使用分类模型的置信度而不是检测置信度
            confidence = float(top5_probs[0])  # 分类模型的置信度
            
            # If tracking is enabled, use tracking history for more consistent labeling
            if use_tracking:
                # 确保所有检测到的物体都能获得跟踪ID
                if i in box_to_id:
                    object_id = box_to_id[i]
                    object_ids.append(object_id)
                    
                    # Add this classification to the object's history
                    self.tracker.add_classification(object_id, label, confidence)
                    
                    # Get the best label based on history
                    best_label = self.tracker.get_best_label(object_id, confidence_threshold)
                    if best_label:
                        label = best_label
                else:
                    # 如果box_to_id中没有此索引，说明跟踪器没有正确处理这个检测框
                    # 手动注册一个新ID
                    x1, y1, x2, y2 = map(int, box.cpu().numpy())
                    cx = (x1 + x2) / 2.0
                    cy = (y1 + y2) / 2.0
                    object_id = self.tracker.register((cx, cy))
                    box_to_id[i] = object_id
                    object_ids.append(object_id)
                    
                    # 添加分类信息
                    self.tracker.add_classification(object_id, label, confidence)
            else:
                object_ids.append(-1)  # No tracking ID
                
            labels.append(label)

        # 创建一个新的列表来存储分类器的置信度，而不是YOLO的置信度
        classifier_confidences = []
        for i, top5_detection in enumerate(detections_list):
            if top5_detection and len(top5_detection) > 0:
                # 使用分类器的置信度
                classifier_confidences.append(float(top5_detection[0]['score']))
            else:
                # 如果没有分类器置信度，则使用YOLO的置信度作为后备
                classifier_confidences.append(confidences[i])
                
        # Store results for potential future reference
        results = {
            'boxes': boxes.tolist(),
            'labels': labels,
            'confidences': classifier_confidences,  # 使用分类器的置信度
            'object_ids': object_ids,
            'detections_list': detections_list
        }
        
        self.last_frame = image_array.copy()
        self.last_results = results
        
        return results

    def process_and_format_results(self, detections: Dict, confidence_threshold: float) -> Dict[str, Any]:
        """Convert detections to API response format with hierarchical classification"""
        api_boxes = []

        try:
            bird_data = pd.read_excel('鸟类目科属种.xlsx', sheet_name='Sheet1')
        except Exception as e:
            logger.error(f"Error loading bird taxonomy data: {str(e)}")
            bird_data = None

        for i, (box, label, conf) in enumerate(zip(detections['boxes'], detections['labels'], detections['confidences'])):
            box_coords = [int(coord) for coord in box]
            
            top5_detections = detections['detections_list'][i] if 'detections_list' in detections and i < len(detections['detections_list']) else [{'name': label, 'score': str(round(float(conf), 6))}]
            
            object_id = detections['object_ids'][i] if 'object_ids' in detections and i < len(detections['object_ids']) else -1
            
            classification_info = {}
            if object_id != -1 and object_id in self.tracker.best_classifications:
                best_info = self.tracker.best_classifications[object_id]
                #confidence_threshold = 0.7  # 可根据需要调整
                best_label = self.tracker.get_best_label(object_id, confidence_threshold)
                
                # 如果所有层级置信度都低于阈值，best_label为None
                if best_label is None:
                    # 使用默认分类类型，避免低置信度的种名被错误地与其他层级置信度绑定
                    classification_type = 'order'  # 使用已有的层级类型，而不是'bird'
                    # 使用通用标签'鸟'
                    best_label = '鸟'
                else:
                    hierarchy_info = self.tracker.taxonomy.get_taxonomy(best_label)
                    classification_type = 'bird'
                    if hierarchy_info:
                        if best_label == hierarchy_info['species']:
                            classification_type = 'species'
                        elif best_label == hierarchy_info['genus']:
                            classification_type = 'genus'
                        elif best_label == hierarchy_info['family']:
                            classification_type = 'family'
                        elif best_label == hierarchy_info['order']:
                            classification_type = 'order'
                
                classification_info = {
                    'classification_type': classification_type,
                    'classification': best_label,
                    'species_score': best_info['species']['score'],
                    'genus_score': best_info['genus']['score'],
                    'family_score': best_info['family']['score'],
                    'order_score': best_info['order']['score'],
                    'species_name': best_info['species']['name'],
                    'genus_name': best_info['genus']['name'],
                    'family_name': best_info['family']['name'],
                    'order_name': best_info['order']['name']
                }
                # 使用最佳层级对应的置信度作为score
                # 确保classification_type是有效的键
                if classification_type in ['species', 'genus', 'family', 'order']:
                    final_score = best_info[f'{classification_type}']['score']
                else:
                    # 如果是自定义类型（如'order'表示'鸟'），使用order的分数
                    final_score = best_info['order']['score']
            else:
                classification_info = self._apply_hierarchical_classification(top5_detections, bird_data,confidence_threshold)
                final_score = float(conf)
            
            # 创建符合模板的基本结构
            box_info = {
                'name': classification_info.get('classification', label),
                'score': final_score,
                'box': box_coords
            }
            
            # 添加额外信息作为补充键值对
            box_info['detections'] = top5_detections
            box_info['classification_type'] = classification_info.get('classification_type', 'name')
            box_info['classification'] = classification_info.get('classification', label)
            box_info['species_score'] = classification_info.get('species_score', final_score)
            box_info['genus_score'] = classification_info.get('genus_score', final_score)
            box_info['family_score'] = classification_info.get('family_score', final_score)
            box_info['order_score'] = classification_info.get('order_score', final_score)
            box_info['species_name'] = classification_info.get('species_name', label)
            box_info['genus_name'] = classification_info.get('genus_name', None)
            box_info['family_name'] = classification_info.get('family_name', None)
            box_info['order_name'] = classification_info.get('order_name', None)
            
            if object_id != -1:
                box_info['tracking_id'] = int(object_id)
            
            api_boxes.append(box_info)

        return {
            'result': 1 if api_boxes else 0,
            'msg': 'found target' if api_boxes else 'no target found',
            'boxes': api_boxes
        }
        
    def _apply_hierarchical_classification(self, detections, bird_data, confidence_threshold=0.7):
        """应用层级分类逻辑，同时计算目、科、属、种四个层级的概率"""
        if bird_data is None or len(detections) == 0:
            # 如果detections为空，返回默认值
            if len(detections) == 0:
                return {
                    'classification_type': 'order', 
                    'classification': '鸟',
                    'species_score': 0.0,
                    'genus_score': 0.0,
                    'family_score': 0.0,
                    'order_score': 0.0
                }
            # 否则使用第一个检测结果
            return {
                'classification_type': 'name', 
                'classification': detections[0]['name'],
                'species_score': float(detections[0]['score']),
                'genus_score': float(detections[0]['score']),
                'family_score': float(detections[0]['score']),
                'order_score': float(detections[0]['score'])
            }
            
        # 找到score最高的detection
        highest_score_detection = detections[0]  # 已经按置信度排序
        highest_score_name = highest_score_detection['name']
        highest_score = float(highest_score_detection['score'])
        
        # 初始化各层级的分类和概率
        species_name = highest_score_name
        species_score = highest_score
        genus_name = None
        genus_score = 0.0
        family_name = None
        family_score = 0.0
        order_name = None
        order_score = 0.0
        
        # 获取当前鸟类的分类信息
        try:
            # 获取当前鸟类的属、科、目信息
            genus_values = bird_data.loc[bird_data['中文名'] == highest_score_name, '属'].values
            family_values = bird_data.loc[bird_data['中文名'] == highest_score_name, '科'].values
            order_values = bird_data.loc[bird_data['中文名'] == highest_score_name, '目'].values
            
            # 如果找到了分类信息
            if genus_values.size > 0 and family_values.size > 0 and order_values.size > 0:
                genus_name = genus_values[0]
                family_name = family_values[0]
                order_name = order_values[0]
                
                # 查找同属、同科、同目的鸟类
                same_genus_birds = bird_data[bird_data['属'] == genus_name]['中文名'].tolist()
                same_family_birds = bird_data[bird_data['科'] == family_name]['中文名'].tolist()
                same_order_birds = bird_data[bird_data['目'] == order_name]['中文名'].tolist()
                
                # 计算各层级的概率总和
                genus_score = species_score  # 初始化为种的概率
                family_score = species_score
                order_score = species_score
                
                # 累加同属、同科、同目鸟类的概率
                for detection in detections[1:]:  # 跳过第一个（已经计算过）
                    bird_name = detection['name']
                    bird_score = float(detection['score'])
                    
                    # 累加同属鸟类的概率
                    if bird_name in same_genus_birds:
                        genus_score += bird_score
                        family_score += bird_score  # 同属的鸟类也是同科同目的
                        order_score += bird_score
                    # 累加同科但不同属鸟类的概率
                    elif bird_name in same_family_birds:
                        family_score += bird_score
                        order_score += bird_score  # 同科的鸟类也是同目的
                    # 累加同目但不同科鸟类的概率
                    elif bird_name in same_order_birds:
                        order_score += bird_score
        except Exception as e:
            logger.error(f"计算层级概率时出错: {str(e)}")
        
        # 确定最终分类类型和结果
        classification_type = 'name'
        classification = species_name
        
        # 如果种的概率大于0.7，直接返回种名
        if species_score > confidence_threshold:
            classification_type = 'species'
            classification = species_name
        # 否则，如果属的概率大于0.7，返回属名
        elif genus_score > confidence_threshold and genus_name:
            classification_type = 'genus'
            classification = genus_name
        # 否则，如果科的概率大于0.7，返回科名
        elif family_score > confidence_threshold and family_name:
            classification_type = 'family'
            classification = family_name
        # 否则，如果目的概率大于0.7，返回目名
        elif order_score > confidence_threshold and order_name:
            classification_type = 'order'
            classification = order_name
        
        # 返回包含所有层级概率的结果
        return {
            'classification_type': classification_type,
            'classification': classification,
            'species_score': round(species_score, 6),
            'genus_score': round(genus_score, 6),
            'family_score': round(family_score, 6),
            'order_score': round(order_score, 6),
            'species_name': species_name,
            'genus_name': genus_name,
            'family_name': family_name,
            'order_name': order_name
        }


# Initialize Flask app
app = Flask(__name__)

# Initialize BirdDetector globally
detector = BirdDetector(
    yolo_path='./best0313.pt',
    classifier_path='./bestswin0610.pth',
    class_dict_path='./swin_transformer.json'
)


@app.route('/agent/aidetect', methods=['POST'])
def detect():
    try:
        # Validate request
        if 'f0' not in request.files:
            return jsonify({
                'result': 0,
                'msg': 'No image file provided',
                'boxes': []
            }), 400

        # Get parameters
        camid = request.form.get('camid')
        if not camid:
            return jsonify({
                'result': 0,
                'msg': 'camid is required',
                'boxes': []
            }), 400

        thresh = float(request.form.get('thresh', 0.5))
        user_thresh = float(request.form.get('confidence_thresh', 0.7))
        # Read and decode image
        image_file = request.files['f0']
        image_bytes = image_file.read()
        nparr = np.frombuffer(image_bytes, np.uint8)
        image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

        if image is None:
            return jsonify({
                'result': 0,
                'msg': 'Invalid image format',
                'boxes': []
            }), 400

        # Process image with threshold
        logger.info(f"Processing image for camid: {camid}")
        detections = detector.detect_and_classify(image, threshold=thresh, confidence_threshold=user_thresh)
        results = detector.process_and_format_results(detections,user_thresh)

        logger.info(f"Detection completed for camid: {camid}")
        return jsonify(results)

    except Exception as e:
        logger.error(f"Error processing request: {str(e)}")
        return jsonify({
            'result': 0,
            'msg': f'Error: {str(e)}',
            'boxes': []
        }), 500


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)  # Adjust port as needed
