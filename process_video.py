import cv2
import numpy as np
import requests
import json
from tqdm import tqdm
from PIL import Image, ImageDraw, ImageFont

def draw_box(frame, box_info, confidence_threshold=0.5):
    # 提取位置信息和文本信息
    box = box_info['box']
    tracking_id = box_info.get('tracking_id', None)
    
    # 坐标转换为整数
    x1, y1, x2, y2 = map(int, box)
    
    # 绘制边框
    cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
    
    # 准备 label 文本
    label_lines = []
    
    # 只显示满足阈值条件的最小分类层级（种、属、科、目）
    # 按照从小到大的层级顺序检查
    label_added = False
    if box_info.get('species_score', 0) > confidence_threshold:
        label_lines.append(f"种: {box_info.get('species_name', '')} ({box_info.get('species_score', 0):.2f})")
        label_added = True
    elif box_info.get('genus_score', 0) > confidence_threshold:
        label_lines.append(f"属: {box_info.get('genus_name', '')} ({box_info.get('genus_score', 0):.2f})")
        label_added = True
    elif box_info.get('family_score', 0) > confidence_threshold:
        label_lines.append(f"科: {box_info.get('family_name', '')} ({box_info.get('family_score', 0):.2f})")
        label_added = True
    elif box_info.get('order_score', 0) > confidence_threshold:
        label_lines.append(f"目: {box_info.get('order_name', '')} ({box_info.get('order_score', 0):.2f})")
        label_added = True
    
    # 如果所有层级都不满足阈值条件，则显示默认标签"鸟"
    if not label_added:
        label_lines.append("鸟")
    
    # 添加跟踪ID（如果有）
    if tracking_id is not None:
        label_lines.append(f'ID:{tracking_id}')
    
    # 将 OpenCV BGR 图像转换为 PIL Image（RGB模式）
    image_pil = Image.fromarray(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
    draw = ImageDraw.Draw(image_pil)
    
    # 加载中文字体 SimHei.ttf（确保该字体文件在工作目录中或者指定完整路径）
    font = ImageFont.truetype("SimHei.ttf", 20)
    
    # 计算并绘制每行文本
    line_height = 25
    for i, line in enumerate(label_lines):
        text_position = (x1, max(y1 - (len(label_lines) - i) * line_height, 0))
        draw.text(text_position, line, font=font, fill=(0, 0, 0))
    
    # 将 PIL Image 转换回 OpenCV 图像（BGR模式）
    frame[:] = cv2.cvtColor(np.array(image_pil), cv2.COLOR_RGB2BGR)

def process_video(input_path, output_path, api_url='http://localhost:5000/agent/aidetect', confidence_threshold=0.55):
    # Open input video
    cap = cv2.VideoCapture(input_path)
    if not cap.isOpened():
        raise ValueError(f"Could not open video file: {input_path}")
    
    # Get video properties
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    # Initialize video writer
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    # Process each frame
    with tqdm(total=total_frames, desc="Processing video") as pbar:
        while cap.isOpened():
            ret, frame = cap.read()
            if not ret:
                break
            
            # Encode frame to jpg for API request
            _, img_encoded = cv2.imencode('.jpg', frame)
            
            # Prepare API request
            files = {'f0': ('frame.jpg', img_encoded.tobytes(), 'image/jpeg')}
            data = { 'camid': '1','thresh': str(confidence_threshold)}
            
            try:
                # Send request to API
                response = requests.post(api_url, files=files, data=data)
                response.raise_for_status()
                result = response.json()
                print("kkkkkkkkkkkkkkkk",result,"jjjjjjjjjjjjjjjj")
                
                # Draw boxes if objects were detected
                if result['result'] == 1:
                    for box_info in result['boxes']:
                        draw_box(frame, box_info, confidence_threshold)
                
                # Write the processed frame
                out.write(frame)
                
            except Exception as e:
                print(f"Error processing frame: {str(e)}")
            
            pbar.update(1)
    
    # Release resources
    cap.release()
    out.release()
    cv2.destroyAllWindows()

if __name__ == '__main__':
    input_video = './0619.mp4'
    output_video = './2.mp4'
    process_video(input_video, output_video)
